{
    find_programver = {
        ["/run/current-system/sw/bin/gcc"] = "14.3.0"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/run/current-system/sw/bin/gcc_14.3.0"] = {
            ["-Xassembler"] = true,
            ["-print-multi-directory"] = true,
            ["-c"] = true,
            ["-dumpversion"] = true,
            ["-shared"] = true,
            ["--help"] = true,
            ["--version"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-pass-exit-codes"] = true,
            ["--target-help"] = true,
            ["-dumpmachine"] = true,
            ["-Xlinker"] = true,
            ["-dumpspecs"] = true,
            ["-print-multi-lib"] = true,
            ["-print-multiarch"] = true,
            ["-save-temps"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-S"] = true,
            ["--param"] = true,
            ["-print-search-dirs"] = true,
            ["-print-multi-os-directory"] = true,
            ["-pipe"] = true,
            ["-x"] = true,
            ["-pie"] = true,
            ["-E"] = true,
            ["-print-sysroot"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-B"] = true,
            ["-v"] = true,
            ["-o"] = true,
            ["-Xpreprocessor"] = true,
            ["-time"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/run/current-system/sw/bin/gcc_14.3.0_cxx__-m64 -m64 -target x86_64-linux-gnu_-fPIC"] = false
    },
    find_program_cross_arch_x86_64_plat_linux_checktoolcxx = {
        gcc = "/run/current-system/sw/bin/gcc"
    },
    find_program = {
        ["/run/current-system/sw/bin/gcc"] = "/run/current-system/sw/bin/gcc"
    }
}