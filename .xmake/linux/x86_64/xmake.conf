{
    __toolchains_linux_x86_64 = {
        "envs",
        "gcc",
        "yasm",
        "nasm",
        "fasm",
        "cuda",
        "go",
        "rust",
        "swift",
        "gfortran",
        "fpc"
    },
    __toolchains_linux_x86_64_host = {
        "envs",
        "gcc",
        "yasm",
        "nasm",
        "fasm",
        "cuda",
        "go",
        "rust",
        "swift",
        "gfortran",
        "fpc"
    },
    arch = "x86_64",
    builddir = "build",
    ccache = true,
    host = "linux",
    kind = "static",
    mode = "release",
    ndk_stdcxx = true,
    plat = "linux"
}