# osu! API Test Application

osu! APIを使用してユーザー情報を取得するGUIアプリケーションです。

## セットアップ

### 1. 依存関係のインストール

必要なライブラリをインストールしてください：
- GLFW
- GLEW
- Dear I<PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON>/json
- libcurl

### 2. 設定ファイルの作成

1. `config.txt.template`を`config.txt`にコピーします：
   ```bash
   cp config.txt.template config.txt
   ```

2. `config.txt`を編集して、あなたのosu! API認証情報を入力します：
   ```
   OSU_CLIENT_ID=あなたのクライアントID
   OSU_CLIENT_SECRET=あなたのクライアントシークレット
   ```

### 3. osu! API認証情報の取得

1. [osu! website](https://osu.ppy.sh/home/<USER>/edit#oauth)にアクセス
2. 「New OAuth Application」をクリック
3. アプリケーション名を入力（例：「My osu! API Test」）
4. Application Callback URLは空白のままでOK
5. 作成されたClient IDとClient Secretを`config.txt`に記入

### 4. ビルドと実行

```bash
# ビルド
make

# 実行
./osuapitest
```

## セキュリティについて

- `config.txt`ファイルには機密情報が含まれているため、Gitリポジトリにはコミットされません
- 設定ファイルは`.gitignore`に追加されています
- 他の人とコードを共有する際は、`config.txt.template`を参考に各自で設定ファイルを作成してください

## 使用方法

1. アプリケーションを起動
2. User IDフィールドにosu!ユーザーIDを入力
3. 「Get User Data」ボタンをクリック
4. ユーザー情報がJSON形式で表示されます
