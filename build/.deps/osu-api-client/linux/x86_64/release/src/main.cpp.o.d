{
    values = {
        "/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++",
        {
            "-m64",
            "-fvisibility=hidden",
            "-fvisibility-inlines-hidden",
            "-O3",
            "-std=c++17",
            "-Isrc",
            "-isystem",
            "/nix/store/0dkdzn9yj8rp2a1qah8s8ymp457cg8vq-nlohmann_json-3.11.3/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/o/openssl/1.1.1-w/6c51ab6278e2479b883dffafac69fdaf/include",
            "-DNDEBUG"
        }
    },
    depfiles_format = "gcc",
    depfiles = "build/.objs/osu-api-client/linux/x86_64/release/src/main.cpp.o:  src/main.cpp src/OsuApiV2Client.h\
",
    files = {
        "src/main.cpp"
    }
}