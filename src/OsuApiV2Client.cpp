// src/OsuApiV2Client.cpp
#include "OsuApiV2Client.h"
#include <iostream>
#include <cstdlib> // std::getenv を使うために追加
#include "cpr/cpr.h"

// 定数の初期化
const std::string OsuApiV2Client::API_BASE_URL = "https://osu.ppy.sh/api/v2";
const std::string OsuApiV2Client::TOKEN_URL = "https://osu.ppy.sh/oauth/token";
const std::chrono::seconds OsuApiV2Client::TOKEN_EXPIRATION_BUFFER(60);

// SSLオプションを生成するヘルパー関数
// Nix環境でSSL証明書のパスを正しく設定するために必要
cpr::SslOptions GetSslOptions() {
    cpr::SslOptions sslOpts;
    // NixはNIX_SSL_CERT_FILE環境変数に証明書バンドルのパスを設定する
    const char* ca_bundle_path = std::getenv("NIX_SSL_CERT_FILE");
    if (ca_bundle_path != nullptr) {
        // 新しいcprでは、証明書のパスを文字列として直接代入する
        sslOpts.ca_info = ca_bundle_path;
    }
    return sslOpts;
}

OsuApiV2Client::OsuApiV2Client(const std::string& client_id, const std::string& client_secret)
    : m_client_id(client_id), m_client_secret(client_secret), m_expires_at(std::chrono::system_clock::now()) {}

void OsuApiV2Client::getNewToken() {
    std::cout << "新しいアクセストークンを取得します..." << std::endl;

    cpr::Response r = cpr::Post(cpr::Url{TOKEN_URL},
                                cpr::Payload{
                                    {"client_id", m_client_id},
                                    {"client_secret", m_client_secret},
                                    {"grant_type", "client_credentials"},
                                    {"scope", "public"}
                                },
                                GetSslOptions()); // SSLオプションを適用

    if (r.status_code != 200) {
        std::cerr << "トークンの取得に失敗しました: " << r.status_code << " - " << r.text << std::endl;
        throw std::runtime_error("Failed to retrieve access token.");
    }

    try {
        json token_data = json::parse(r.text);
        m_access_token = token_data["access_token"].get<std::string>();
        long long expires_in_seconds = token_data["expires_in"].get<long long>();

        m_expires_at = std::chrono::system_clock::now()
                     + std::chrono::seconds(expires_in_seconds)
                     - TOKEN_EXPIRATION_BUFFER;

        std::cout << "トークン取得成功。" << std::endl;
    } catch (const json::parse_error& e) {
        std::cerr << "JSONのパースに失敗しました: " << e.what() << std::endl;
        throw;
    }
}

void OsuApiV2Client::ensureToken() {
    if (m_access_token.empty() || std::chrono::system_clock::now() >= m_expires_at) {
        getNewToken();
    }
}

std::optional<json> OsuApiV2Client::makeRequest(const std::string& endpoint) {
    try {
        ensureToken();

        cpr::Response r = cpr::Get(cpr::Url{API_BASE_URL + "/" + endpoint},
                                   cpr::Header{
                                       {"Authorization", "Bearer " + m_access_token},
                                       {"Accept", "application/json"},
                                       {"Content-Type", "application/json"}
                                   },
                                   GetSslOptions()); // SSLオプションを適用

        if (r.status_code != 200) {
            std::cerr << "APIリクエストに失敗しました (" << endpoint << "): " << r.status_code << " - " << r.text << std::endl;
            return std::nullopt;
        }

        return json::parse(r.text);

    } catch (const std::exception& e) {
        std::cerr << "リクエスト中に例外が発生しました: " << e.what() << std::endl;
        return std::nullopt;
    }
}

std::optional<json> OsuApiV2Client::getUser(int user_id, const std::string& mode) {
    std::string endpoint = "users/" + std::to_string(user_id) + "/" + mode;
    return makeRequest(endpoint);
}

std::optional<json> OsuApiV2Client::getBeatmap(int beatmap_id) {
    std::string endpoint = "beatmaps/" + std::to_string(beatmap_id);
    return makeRequest(endpoint);
}