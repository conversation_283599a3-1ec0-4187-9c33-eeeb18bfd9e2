// src/main.cpp (GUI版)
#include <iostream>
#include <future>   // 非同期処理のため
#include <string>

// GLEW (OpenGL拡張)
#define GLEW_STATIC
#include <GL/glew.h>

// GLFW (ウィンドウ)
#include <GLFW/glfw3.h>

// Dear ImGui
#include "imgui.h"
#include "imgui_impl_glfw.h"
#include "imgui_impl_opengl3.h"

// 既存のAPIクライアント
#include "OsuApiV2Client.h"
#include "ConfigLoader.h"

// APIリクエストの状態を管理
enum class ApiState {
    Idle,
    Loading,
    Success,
    Error
};

// GLFWのエラーコールバック
static void glfw_error_callback(int error, const char* description) {
    fprintf(stderr, "Glfw Error %d: %s\n", error, description);
}

int main(int, char**) {
    // ===================================================================
    // 設定ファイルから認証情報を読み込み
    // ===================================================================
    auto config = ConfigLoader::loadConfig("config.txt");
    if (!ConfigLoader::validateConfig(config)) {
        std::cerr << "エラー: config.txtファイルを確認してください。" << std::endl;
        std::cerr << "ファイル形式:" << std::endl;
        std::cerr << "OSU_CLIENT_ID=あなたのクライアントID" << std::endl;
        std::cerr << "OSU_CLIENT_SECRET=あなたのクライアントシークレット" << std::endl;
        return 1;
    }

    const std::string OSU_CLIENT_ID = config["OSU_CLIENT_ID"];
    const std::string OSU_CLIENT_SECRET = config["OSU_CLIENT_SECRET"];

    OsuApiV2Client osuClient(OSU_CLIENT_ID, OSU_CLIENT_SECRET);


    // --- 1. GLFWの初期化とウィンドウ作成 ---
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) return 1;

    // OpenGL 3.3, GLSL 330
    const char* glsl_version = "#version 330";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

    GLFWwindow* window = glfwCreateWindow(800, 600, "osu! API Client with ImGui", NULL, NULL);
    if (window == NULL) return 1;
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // VSyncを有効に

    // --- 2. GLEWの初期化 ---
    if (glewInit() != GLEW_OK) {
        fprintf(stderr, "Failed to initialize GLEW\n");
        return 1;
    }

    // --- 3. ImGuiの初期化 ---
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    ImGui::StyleColorsDark();
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // --- 4. アプリケーションの状態変数 ---
    char userIdBuf[128] = "2"; // ユーザーID入力バッファ
    ApiState currentState = ApiState::Idle;
    std::string resultText = "";
    // API呼び出しを非同期で実行するためのfuture
    std::future<std::optional<json>> apiFuture;


    // --- 5. メインループ ---
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // ImGuiのフレームを開始
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // UIの描画
        {
            ImGui::Begin("osu! User Info");

            ImGui::InputText("User ID", userIdBuf, IM_ARRAYSIZE(userIdBuf));

            // ローディング中はボタンを無効化
            if (currentState == ApiState::Loading) {
                ImGui::BeginDisabled();
            }

            if (ImGui::Button("Get User Data")) {
                // ボタンが押されたら非同期でAPIを呼び出す
                currentState = ApiState::Loading;
                resultText = "Loading...";
                apiFuture = std::async(std::launch::async, [&]() {
                    int userId = std::stoi(userIdBuf);
                    return osuClient.getUser(userId);
                });
            }

            if (currentState == ApiState::Loading) {
                ImGui::EndDisabled();
                ImGui::SameLine();
                ImGui::Text("Loading...");
            }
            
            ImGui::Separator();
            ImGui::Text("Result:");
            ImGui::TextWrapped("%s", resultText.c_str());

            ImGui::End();
        }

        // 非同期処理の結果をチェック
        if (currentState == ApiState::Loading) {
            // futureが結果を返せるか0秒待機でチェック（ノンブロッキング）
            if (apiFuture.wait_for(std::chrono::seconds(0)) == std::future_status::ready) {
                try {
                    auto result = apiFuture.get();
                    if (result) {
                        resultText = result->dump(4); // JSONを整形して表示
                        currentState = ApiState::Success;
                    } else {
                        resultText = "Failed to get user data. Check console for details.";
                        currentState = ApiState::Error;
                    }
                } catch (const std::exception& e) {
                    resultText = "An exception occurred: " + std::string(e.what());
                    currentState = ApiState::Error;
                }
            }
        }

        // レンダリング
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);
        glViewport(0, 0, display_w, display_h);
        glClearColor(0.45f, 0.55f, 0.60f, 1.00f);
        glClear(GL_COLOR_BUFFER_BIT);

        ImGui::Render();
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // --- 6. クリーンアップ ---
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}