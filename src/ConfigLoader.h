#pragma once
#include <string>
#include <fstream>
#include <sstream>
#include <unordered_map>
#include <iostream>

class ConfigLoader {
public:
    static std::unordered_map<std::string, std::string> loadConfig(const std::string& filename) {
        std::unordered_map<std::string, std::string> config;
        std::ifstream file(filename);
        
        if (!file.is_open()) {
            std::cerr << "エラー: 設定ファイル '" << filename << "' を開けませんでした。" << std::endl;
            return config;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            // 空行やコメント行（#で始まる）をスキップ
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            // KEY=VALUE形式をパース
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);
                
                // 前後の空白を削除
                key.erase(0, key.find_first_not_of(" \t"));
                key.erase(key.find_last_not_of(" \t") + 1);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t") + 1);
                
                config[key] = value;
            }
        }
        
        file.close();
        return config;
    }
    
    static bool validateConfig(const std::unordered_map<std::string, std::string>& config) {
        if (config.find("OSU_CLIENT_ID") == config.end() || 
            config.find("OSU_CLIENT_SECRET") == config.end()) {
            std::cerr << "エラー: 設定ファイルにOSU_CLIENT_IDまたはOSU_CLIENT_SECRETが見つかりません。" << std::endl;
            return false;
        }
        
        if (config.at("OSU_CLIENT_ID").empty() || config.at("OSU_CLIENT_SECRET").empty()) {
            std::cerr << "エラー: OSU_CLIENT_IDまたはOSU_CLIENT_SECRETが空です。" << std::endl;
            return false;
        }
        
        return true;
    }
};
