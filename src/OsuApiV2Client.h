#pragma once

#include <string>
#include <chrono>
#include <optional>
#include "nlohmann/json.hpp" // JSON for Modern C++

// nlohmann::jsonを使いやすくするためにエイリアスを定義
using json = nlohmann::json;

class OsuApiV2Client {
public:
    // コンストラクタ
    OsuApiV2Client(const std::string& client_id, const std::string& client_secret);

    // APIメソッド
    std::optional<json> getUser(int user_id, const std::string& mode = "osu");
    std::optional<json> getBeatmap(int beatmap_id);

private:
    // トークンが有効か確認し、必要なら更新する
    void ensureToken();
    // 新しいトークンを取得する
    void getNewToken();
    // APIリクエストを行う共通メソッド
    std::optional<json> makeRequest(const std::string& endpoint);

    // メンバ変数
    std::string m_client_id;
    std::string m_client_secret;
    std::string m_access_token;

    // トークンの絶対的な有効期限を保持
    std::chrono::time_point<std::chrono::system_clock> m_expires_at;

    // 定数
    static const std::string API_BASE_URL;
    static const std::string TOKEN_URL;
    // 安全マージン（60秒）
    static const std::chrono::seconds TOKEN_EXPIRATION_BUFFER;
};