-- xmake.lua (GUI対応版)
add_rules("mode.debug", "mode.release")
set_project("OsuApiClientExample")
set_version("1.0.0")

set_languages("cxx17")

-- 既存の依存関係
add_requires("nlohmann_json", { optional = true })
add_requires("cpr", { optional = true })

-- ★★ ImGuiの依存関係を追加 ★★
-- GLFWとOpenGL3のバックエンドを有効にするようconfigsで指定する
add_requires("imgui", {configs = {glfw = true, opengl3 = true}})

target("osu-api-client") do
    set_kind("binary")
    add_files("src/*.cpp")
    add_includedirs("src")

    -- リンクするパッケージにimguiを追加
    add_packages("nlohmann_json", "cpr", "imgui")

    -- Linux環境で必要になる可能性のあるシステムライブラリ
    -- imguiパッケージが自動で処理してくれることが多いが、念のため
    if is_plat("linux") then
        add_packages("glew", "glfw3", "opengl")
        add_syslinks("GLE<PERSON>", "glfw", "GL", "dl", "pthread")
    end
end