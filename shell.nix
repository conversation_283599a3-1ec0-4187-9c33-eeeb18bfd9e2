# shell.nix (GUI対応版)
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = [
    # 1. C++開発の基本ツール
    pkgs.xmake
    pkgs.clang_16
    pkgs.pkg-config

    # 2. 既存のネットワーク関連ライブラリ
    pkgs.curl
    pkgs.openssl
    pkgs.zlib
    pkgs.cacert

    # 3. GUI (ImGui) に必要なシステムライブラリを追加
    pkgs.glfw        # ウィンドウ管理
    pkgs.glew        # OpenGL拡張機能の読み込み
    pkgs.mesa    # OpenGL開発用ヘッダ
    pkgs.xorg.libX11 # 以下、Linuxデスクトップ環境で必要
    pkgs.xorg.libXcursor
    pkgs.xorg.libXrandr
    pkgs.xorg.libXi

    pkgs.nlohmann_json
    pkgs.cpr
  ];

  # (shellHookは変更なし)
  shellHook = ''
    echo ""
    echo "osu! API Client C++ (GUI) 開発環境へようこそ！"
    echo "------------------------------------------------"
    echo "  xmake run     - ビルドして実行"
    echo "------------------------------------------------"
    echo ""
  '';
}